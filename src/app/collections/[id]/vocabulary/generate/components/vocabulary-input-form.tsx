'use client';

import { Button, Card, Input, Label, Translate } from '@/components/ui';
import { useTranslation } from '@/contexts';
import { useToast } from '@/contexts/toast-context';
import { WordDetail } from '@/models';
import { Language } from '@prisma/client';
import { BookOpen, Plus, X } from 'lucide-react';
import { useCallback, useState } from 'react';

interface VocabularyInputFormProps {
	onWordsGenerated: (words: WordDetail[]) => void;
	sourceLanguage: Language;
	targetLanguage: Language;
	isLoading?: boolean;
}

export function VocabularyInputForm({
	onWordsGenerated,
	sourceLanguage,
	targetLanguage,
	isLoading = false,
}: VocabularyInputFormProps) {
	const { t } = useTranslation();
	const { showError } = useToast();
	const [inputTerms, setInputTerms] = useState<string[]>([]);
	const [newTerm, setNewTerm] = useState('');
	const [isGenerating, setIsGenerating] = useState(false);

	const handleAddTerm = useCallback(() => {
		if (!newTerm.trim()) return;

		const trimmedTerm = newTerm.trim();

		// Check if term already exists
		if (inputTerms.includes(trimmedTerm.toLowerCase())) {
			showError(new Error(t('vocabulary_input.term_already_exists')));
			return;
		}

		// Check maximum limit
		if (inputTerms.length >= 10) {
			showError(new Error(t('vocabulary_input.max_terms_reached')));
			return;
		}

		setInputTerms([...inputTerms, trimmedTerm]);
		setNewTerm('');
	}, [newTerm, inputTerms, showError, t]);

	const handleKeyPress = useCallback(
		(e: React.KeyboardEvent<HTMLInputElement>) => {
			if (e.key === 'Enter') {
				e.preventDefault();
				handleAddTerm();
			}
		},
		[handleAddTerm]
	);

	const handleRemoveTerm = useCallback(
		(termToRemove: string) => {
			setInputTerms(inputTerms.filter((term) => term !== termToRemove));
		},
		[inputTerms]
	);

	const handleGenerateDetails = useCallback(async () => {
		if (inputTerms.length === 0) {
			showError(new Error(t('vocabulary_input.no_terms_entered')));
			return;
		}

		setIsGenerating(true);
		try {
			const response = await fetch('/api/llm/generate-word-details', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					terms: inputTerms,
					source_language: sourceLanguage,
					target_language: targetLanguage,
				}),
			});

			if (!response.ok) {
				const error = await response.json();
				throw new Error(error.error || 'Failed to generate word details');
			}

			const wordDetails: WordDetail[] = await response.json();
			onWordsGenerated(wordDetails);

			// Clear input after successful generation
			setInputTerms([]);
		} catch (error) {
			const err =
				error instanceof Error ? error : new Error('Failed to generate word details');
			showError(err);
		} finally {
			setIsGenerating(false);
		}
	}, [inputTerms, sourceLanguage, targetLanguage, onWordsGenerated, showError, t]);

	return (
		<div className="space-y-4">
			<Card className="p-7 rounded-2xl shadow-2xl bg-card">
				<div className="flex flex-col gap-3">
					<div className="flex items-center gap-2 mb-2">
						<BookOpen className="h-6 w-6 text-primary" />
						<Label className="text-lg font-bold text-primary tracking-tight">
							<Translate text="vocabulary_input.title" />
						</Label>
					</div>

					<div className="flex gap-3 items-center">
						<div className="relative flex-1">
							<Input
								type="text"
								value={newTerm}
								onChange={(e) => setNewTerm(e.target.value)}
								onKeyDown={handleKeyPress}
								placeholder={t('vocabulary_input.placeholder')}
								className="h-11 py-2 text-sm rounded-xl focus:ring-2 focus:ring-primary/30 bg-background text-primary shadow-inner placeholder:text-primary/60"
								aria-label={t('vocabulary_input.placeholder')}
								disabled={isLoading || isGenerating}
							/>
						</div>
						<Button
							onClick={handleAddTerm}
							disabled={
								!newTerm.trim() ||
								inputTerms.length >= 10 ||
								isLoading ||
								isGenerating
							}
							size="sm"
							className="h-9 rounded-xl px-4 font-semibold bg-primary text-background shadow-lg hover:bg-primary/90 transition-all duration-200 flex gap-2 items-center text-sm"
						>
							<Plus className="h-4 w-4" />
							<Translate text="ui.add" />
						</Button>
					</div>

					{/* Terms display */}
					<div className="space-y-3">
						<div className="min-h-[36px]">
							{inputTerms.length > 0 && (
								<div className="flex flex-wrap gap-3">
									{inputTerms.map((term, index) => (
										<div key={index} className="relative">
											<span className="inline-flex items-center px-4 py-1.5 rounded-xl font-medium text-sm select-none transition-all duration-200 bg-primary text-background shadow-lg">
												{term}
												<button
													onClick={() => handleRemoveTerm(term)}
													className="ml-2 hover:bg-background/20 rounded-full p-0.5 transition-colors"
													aria-label={t('ui.remove')}
													disabled={isLoading || isGenerating}
												>
													<X className="h-3 w-3" />
												</button>
											</span>
										</div>
									))}
								</div>
							)}
						</div>

						{/* Terms count and limit info */}
						{inputTerms.length > 0 && (
							<div className="text-sm text-primary/80 font-medium">
								<Translate
									text="vocabulary_input.terms_count"
									values={{ count: inputTerms.length, max: 10 }}
								/>
							</div>
						)}

						{/* Generate button */}
						{inputTerms.length > 0 && (
							<Button
								onClick={handleGenerateDetails}
								disabled={inputTerms.length === 0 || isLoading || isGenerating}
								className="w-full h-12 text-base font-bold rounded-2xl bg-secondary text-secondary-foreground shadow-2xl hover:bg-secondary/90 transition-all duration-200 flex gap-3 items-center justify-center"
								loading={isGenerating}
							>
								<BookOpen className="h-6 w-6" />
								<Translate text="vocabulary_input.generate_details" />
							</Button>
						)}
					</div>
				</div>
			</Card>
		</div>
	);
}
