'use client';

import { <PERSON><PERSON>, <PERSON>, <PERSON>Content, CardHeader, CardTitle, Translate } from '@/components/ui';
import { useTranslation } from '@/contexts';
import { WordDetail } from '@/models';
import { Language } from '@prisma/client';
import { Plus, Undo2 } from 'lucide-react';
import { useCallback } from 'react';
import { cn } from '@/lib/utils';

interface DetailedWordItemProps {
	word: WordDetail;
	onAddToCollection: (word: WordDetail) => Promise<void>;
	onUndoWordAddition?: (word: WordDetail) => Promise<void>;
	isAdded: boolean;
	isAdding: boolean;
	sourceLanguage: Language;
	targetLanguage: Language;
}

function DetailedWordItem({
	word,
	onAddToCollection,
	onUndoWordAddition,
	isAdded,
	isAdding,
	sourceLanguage,
	targetLanguage,
}: DetailedWordItemProps) {
	const { t } = useTranslation();

	const getTranslationKeyOfLanguage = (lang: Language) => {
		return lang === 'EN' ? 'ui.english' : 'ui.vietnamese';
	};

	return (
		<Card className="flex flex-col break-inside-avoid shadow-lg border border-border bg-background hover:shadow-xl transition-shadow duration-200">
			<CardHeader className="py-4 px-5 border-b border-border bg-gradient-to-r from-primary/5 via-primary/10 to-transparent rounded-t-lg">
				<CardTitle className="flex justify-between items-start gap-3">
					<div className="flex-1">
						<h3 className="text-3xl font-bold tracking-tight text-primary drop-shadow-sm mb-2">
							{word.term}
						</h3>
						<div className="flex flex-wrap items-center gap-2 mb-2">
							<span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary border border-primary/20">
								{word.language === 'EN' ? 'English' : 'Vietnamese'}
							</span>
						</div>
					</div>
				</CardTitle>
			</CardHeader>
			<CardContent className="flex-grow flex flex-col p-5">
				<div className="space-y-4 flex-grow">
					{/* Detailed information from WordDetail */}
					{word.definitions && word.definitions.length > 0
						? word.definitions.map((definition, index) => (
								<div
									key={index}
									className="p-4 rounded-xl border border-border/70 bg-accent/25 hover:bg-accent/40 transition-colors duration-150 dark:bg-accent/15 dark:hover:bg-accent/30"
								>
									{definition.pos && definition.pos.length > 0 && (
										<p className="text-xs font-semibold uppercase tracking-wider text-primary/90 mb-2.5">
											{definition.pos.join(', ')}
										</p>
									)}
									{definition.ipa && (
										<p className="text-sm text-muted-foreground italic mb-2.5">
											IPA: {definition.ipa}
										</p>
									)}

									{/* Explanations */}
									{definition.explains && definition.explains.length > 0 && (
										<div className="space-y-2 mb-3">
											{definition.explains.map((explain, explainIndex) => (
												<div key={explainIndex} className="space-y-1">
													<div className="pl-3 border-l-2 border-primary/30 py-1">
														<p className="text-xs font-medium text-muted-foreground tracking-wide">
															<Translate
																text={getTranslationKeyOfLanguage(
																	targetLanguage
																)}
															/>
															:
														</p>
														<p className="mb-1 text-sm text-foreground/95">
															{explain[targetLanguage]}
														</p>
													</div>
													{sourceLanguage !== targetLanguage && (
														<div className="pl-3 border-l-2 border-secondary/30 py-1">
															<p className="text-xs font-medium text-muted-foreground tracking-wide">
																<Translate
																	text={getTranslationKeyOfLanguage(
																		sourceLanguage
																	)}
																/>
																:
															</p>
															<p className="text-sm text-foreground/80">
																{explain[sourceLanguage]}
															</p>
														</div>
													)}
												</div>
											))}
										</div>
									)}

									{/* Examples */}
									{definition.examples && definition.examples.length > 0 && (
										<div className="space-y-2">
											<p className="text-xs font-semibold uppercase tracking-wider text-secondary/90 mb-2">
												<Translate text="words.examples" />
											</p>
											{definition.examples.map((example, exampleIndex) => (
												<div key={exampleIndex} className="space-y-1">
													<div className="pl-3 border-l-2 border-primary/30 py-1">
														<p className="text-xs font-medium text-muted-foreground tracking-wide">
															<Translate
																text={getTranslationKeyOfLanguage(
																	targetLanguage
																)}
															/>
															:
														</p>
														<p className="mb-1 text-sm text-foreground/95 italic">
															&ldquo;{example[targetLanguage]}&rdquo;
														</p>
													</div>
													{sourceLanguage !== targetLanguage && (
														<div className="pl-3 border-l-2 border-secondary/30 py-1">
															<p className="text-xs font-medium text-muted-foreground tracking-wide">
																<Translate
																	text={getTranslationKeyOfLanguage(
																		sourceLanguage
																	)}
																/>
																:
															</p>
															<p className="text-sm text-foreground/80 italic">
																&ldquo;{example[sourceLanguage]}
																&rdquo;
															</p>
														</div>
													)}
												</div>
											))}
										</div>
									)}
								</div>
						  ))
						: null}
				</div>

				{/* Action buttons */}
				<div className="flex gap-2 mt-4 pt-4 border-t border-border/50">
					{!isAdded ? (
						<Button
							onClick={() => onAddToCollection(word)}
							disabled={isAdding}
							size="sm"
							className="flex-1 h-10 rounded-xl font-semibold bg-primary text-background shadow-lg hover:bg-primary/90 transition-all duration-200 flex gap-2 items-center justify-center text-sm"
							loading={isAdding}
						>
							<Plus className="h-4 w-4" />
							<Translate text="words.add_to_collection" />
						</Button>
					) : (
						<div className="flex gap-2 flex-1">
							<div className="flex-1 h-10 rounded-xl font-semibold bg-green-500 text-white shadow-lg flex gap-2 items-center justify-center text-sm">
								<Translate text="words.added" />
							</div>
							{onUndoWordAddition && (
								<Button
									onClick={() => onUndoWordAddition(word)}
									size="sm"
									variant="outline"
									className="h-10 rounded-xl font-semibold border-2 border-primary/30 text-primary hover:bg-primary/10 transition-all duration-200 flex gap-2 items-center text-sm"
								>
									<Undo2 className="h-4 w-4" />
									<Translate text="words.undo" />
								</Button>
							)}
						</div>
					)}
				</div>
			</CardContent>
		</Card>
	);
}

interface DetailedWordListProps {
	words: WordDetail[];
	onAddToCollection: (word: WordDetail) => Promise<void>;
	onUndoWordAddition?: (word: WordDetail) => Promise<void>;
	getLoadingState: (term: string) => {
		adding?: boolean;
	};
	addedWords: Set<string>;
	className?: string;
	sourceLanguage: Language;
	targetLanguage: Language;
}

export function DetailedWordList({
	words,
	onAddToCollection,
	onUndoWordAddition,
	getLoadingState,
	addedWords,
	className,
	sourceLanguage,
	targetLanguage,
}: DetailedWordListProps) {
	const handleAddToCollection = useCallback(
		async (word: WordDetail) => {
			if (getLoadingState(word.term).adding) return;
			await onAddToCollection(word);
		},
		[getLoadingState, onAddToCollection]
	);

	if (!words || words.length === 0) return null;

	return (
		<div className={cn('space-y-4', className)}>
			{words.map((word) => {
				const loadingState = getLoadingState(word.term);

				return (
					<DetailedWordItem
						key={word.id}
						word={word}
						onAddToCollection={handleAddToCollection}
						onUndoWordAddition={onUndoWordAddition}
						isAdded={addedWords.has(word.term)}
						isAdding={loadingState.adding || false}
						sourceLanguage={sourceLanguage}
						targetLanguage={targetLanguage}
					/>
				);
			})}
		</div>
	);
}
