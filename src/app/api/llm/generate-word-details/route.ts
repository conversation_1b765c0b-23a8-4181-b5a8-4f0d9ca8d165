import { ValidationError } from '@/backend/errors';
import { getLLMService } from '@/backend/wire';
import { Language } from '@prisma/client';
import { WordDetail } from '@/models';
import { NextRequest, NextResponse } from 'next/server';
import fs from 'node:fs/promises';
import path from 'node:path';
import { z, ZodError } from 'zod';

// Schema for vocabulary input - user-provided terms for detailed learning
const generateWordDetailsSchema = z.object({
	terms: z
		.array(z.string())
		.min(1, 'At least one term is required')
		.max(10, 'Cannot generate details for more than 10 terms at once'), // Reduced for vocabulary input
	source_language: z.nativeEnum(Language),
	target_language: z.nativeEnum(Language),
});

export async function POST(request: NextRequest) {
	try {
		const body = await request.json();
		const validatedData = generateWordDetailsSchema.parse(body);
		const { terms, source_language, target_language } = validatedData;

		// Create cache file path for development
		const cacheDir = path.join(process.cwd(), '.cache', 'llm');
		const cacheKey = `word-details-${terms.join('-')}-${source_language}-${target_language}`;
		const filePath = path.join(cacheDir, `${cacheKey}.json`);

		// Try to load from cache in development
		if (process.env.NODE_ENV === 'development') {
			try {
				const cachedData = await fs.readFile(filePath, 'utf-8');
				const parsedData = JSON.parse(cachedData);
				return NextResponse.json(parsedData as WordDetail[]);
			} catch (err) {
				// Cache miss, continue with generation
			}
		}

		const llmService = await getLLMService();
		// Use vocabulary-specific method for user-provided terms
		const words = await llmService.generateVocabularyDetails(
			terms,
			source_language,
			target_language
		);

		// Save to cache in development
		if (process.env.NODE_ENV === 'development') {
			try {
				await fs.mkdir(cacheDir, { recursive: true });
				await fs.writeFile(filePath, JSON.stringify(words, null, 2), 'utf-8');
			} catch (err: any) {
				console.error(
					`Development mode: Failed to save word details cache file ${filePath}:`,
					err.message
				);
			}
		}

		return NextResponse.json(words);
	} catch (error) {
		if (error instanceof ZodError) {
			return NextResponse.json({ error: error.errors[0].message }, { status: 400 });
		}

		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		console.error('Error in generateWordDetailsApi:', error);
		return NextResponse.json(
			{ error: 'Failed to generate word details. Please try again.' },
			{ status: 500 }
		);
	}
}
