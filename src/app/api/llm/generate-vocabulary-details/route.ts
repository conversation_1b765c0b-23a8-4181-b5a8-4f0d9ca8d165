import { getLLMService } from '@/backend/wire';
import { Language } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { withAuth, withValidation } from '@/lib/api-error-middleware';

// Schema for vocabulary generation with details - keyword-based generation
const generateVocabularyDetailsSchema = z.object({
	keywordTerms: z
		.array(z.string())
		.min(1, 'At least one keyword is required')
		.max(10, 'Cannot use more than 10 keywords'),
	maxTerms: z.number().min(1).max(20, 'Cannot generate more than 20 terms at once'),
	excludesTerms: z.array(z.string()).default([]),
	excludeCollectionIds: z.array(z.string()).optional(),
	source_language: z.nativeEnum(Language),
	target_language: z.nativeEnum(Language),
});

async function generateVocabularyDetailsHandler(
	_request: NextRequest,
	context: { validatedData: any }
): Promise<NextResponse> {
	const {
		keywordTerms,
		maxTerms,
		excludesTerms,
		excludeCollectionIds,
		source_language,
		target_language,
	} = context.validatedData;

	try {
		const llmService = await getLLMService();

		// Use vocabulary-specific method for keyword-based generation with full details
		const words = await llmService.generateVocabularyWithDetails({
			keywordTerms,
			maxTerms,
			excludesTerms,
			excludeCollectionIds,
			source_language,
			target_language,
		});

		return NextResponse.json({
			success: true,
			data: words,
		});
	} catch (error) {
		console.error('Error generating vocabulary with details:', error);
		return NextResponse.json(
			{
				success: false,
				error:
					error instanceof Error
						? error.message
						: 'Failed to generate vocabulary with details',
			},
			{ status: 500 }
		);
	}
}

export const POST = withAuth(
	withValidation(generateVocabularyDetailsSchema, 'body')(generateVocabularyDetailsHandler)
);
